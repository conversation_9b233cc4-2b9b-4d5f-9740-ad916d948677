"""
Utilitários para o aplicativo de estoque
"""
import re
from decimal import Decimal


def extrair_valor_numerico_diametro(diametro_str):
    """
    Extrai o valor numérico de uma string de diâmetro.

    Exemplos:
    - "0,10 mm" -> 0.1
    - "0.20 mm" -> 0.2
    - "Ø 0,30 mm" -> 0.3
    - "1,00 mm" -> 1.0
    - "1.50 mm" -> 1.5

    Args:
        diametro_str (str): String contendo o diâmetro

    Returns:
        float: Valor numérico do diâmetro ou 0 se não for possível extrair
    """
    if not diametro_str:
        return 0

    # Substituir vírgulas por pontos para garantir formato decimal correto
    diametro_str = diametro_str.replace(',', '.')

    # Remover caracteres não numéricos e manter apenas números e pontos
    valor_numerico = re.sub(r'[^\d.]', '', diametro_str)

    try:
        return float(valor_numerico)
    except (ValueError, TypeError):
        return 0


def extrair_valor_numerico_nome_mola(nome_mola_str):
    """
    Extrai o valor numérico do nome da mola para ordenação.

    Exemplos:
    - "01" -> 1
    - "02" -> 2
    - "19" -> 19
    - "123" -> 123

    Args:
        nome_mola_str (str): String contendo o nome da mola

    Returns:
        int: Valor numérico do nome da mola, ou 0 se não conseguir extrair
    """
    if not nome_mola_str:
        return 0

    # Remove espaços e converte para string
    nome_mola_str = str(nome_mola_str).strip()

    # Extrai apenas números
    match = re.search(r'\d+', nome_mola_str)
    if match:
        try:
            return int(match.group())
        except ValueError:
            return 0

    return 0


def extrair_valor_numerico_codigo_mola(codigo_mola_str):
    """
    Extrai o valor numérico do código da mola para ordenação correta.
    Considera o número após a barra no código da mola.

    Exemplos:
    - "5191/01" -> (5191, 1)
    - "5192/02" -> (5192, 2)
    - "5193/03" -> (5193, 3)
    - "ABC/123" -> (0, 123)
    - "123" -> (123, 0)

    Args:
        codigo_mola_str (str): String contendo o código da mola

    Returns:
        tuple: (numero_antes_barra, numero_depois_barra) para ordenação
    """
    if not codigo_mola_str:
        return (0, 0)

    # Remove espaços e converte para string
    codigo_mola_str = str(codigo_mola_str).strip()

    # Verifica se há barra no código
    if '/' in codigo_mola_str:
        partes = codigo_mola_str.split('/')
        parte_antes = partes[0].strip()
        parte_depois = partes[1].strip() if len(partes) > 1 else ""

        # Extrai números da parte antes da barra
        match_antes = re.search(r'\d+', parte_antes)
        numero_antes = int(match_antes.group()) if match_antes else 0

        # Extrai números da parte depois da barra
        match_depois = re.search(r'\d+', parte_depois)
        numero_depois = int(match_depois.group()) if match_depois else 0

        return (numero_antes, numero_depois)
    else:
        # Se não há barra, extrai apenas o número principal
        match = re.search(r'\d+', codigo_mola_str)
        numero = int(match.group()) if match else 0
        return (numero, 0)


def ordenar_materiais(materiais):
    """
    Ordena uma lista de materiais por nome e diâmetro numérico.
    
    Args:
        materiais (list): Lista de objetos Material ou dicionários com chaves 'nome' e 'diametro'
        
    Returns:
        list: Lista ordenada de materiais
    """
    # Verificar se estamos lidando com objetos Material ou dicionários
    if materiais and hasattr(materiais[0], 'nome'):
        # Objetos Material
        return sorted(materiais, key=lambda m: (m.nome, extrair_valor_numerico_diametro(m.diametro)))
    else:
        # Dicionários
        return sorted(materiais, key=lambda m: (m.get('nome', ''), extrair_valor_numerico_diametro(m.get('diametro', ''))))


def formatar_decimal(valor, casas_decimais=2):
    """
    Formata um valor decimal para exibição.
    
    Args:
        valor: Valor a ser formatado (pode ser Decimal, float, int ou string)
        casas_decimais (int): Número de casas decimais
        
    Returns:
        str: Valor formatado
    """
    try:
        # Converter para Decimal se não for
        if not isinstance(valor, Decimal):
            valor = Decimal(str(valor))
        
        # Formatar com o número correto de casas decimais
        formato = f'{{:.{casas_decimais}f}}'
        return formato.format(valor)
    except:
        return str(valor)
